"""
Recommendation service for OAU program recommendations.
Implements the scoring system: 65% WAEC fit + 35% Interest fit.
"""

import re
import math
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import numpy as np


@dataclass
class WAECResult:
    """Represents a single WAEC subject result."""
    subject: str
    grade: str
    score: float  # Numerical score (0-1)


@dataclass
class ProgramScore:
    """Represents a program recommendation score."""
    program_name: str
    eligibility_passed: bool
    waec_fit: float
    interest_fit: float
    final_score: float
    explanation: str


class RecommendationService:
    """Handles program recommendation scoring and ranking."""
    
    # Grade to score mapping
    GRADE_SCORES = {
        'A1': 1.0,
        'B2': 0.9,
        'B3': 0.8,
        'C4': 0.7,
        'C5': 0.6,
        'C6': 0.5,
        'D7': 0.0,  # Below credit
        'E8': 0.0,  # Below credit
        'F9': 0.0   # Below credit
    }
    
    def __init__(self, embedding_model: str = "models/embedding-001"):
        """Initialize the recommendation service."""
        self.embedding_model = embedding_model
        self.embeddings = None
        
    def initialize_embeddings(self):
        """Initialize the embeddings model."""
        try:
            self.embeddings = GoogleGenerativeAIEmbeddings(model=self.embedding_model)
            return True
        except Exception as e:
            print(f"Error initializing embeddings: {e}")
            return False
    
    def parse_waec_results(self, results_text: str) -> List[WAECResult]:
        """
        Parse WAEC results from text input.
        Expected format: "English (B3), Mathematics (A1), Physics (A1), Chemistry (B2), Biology (B3)"
        """
        waec_results = []
        
        # Pattern to match subject (grade) format
        pattern = r'([A-Za-z\s]+)\s*\(([A-Z]\d)\)'
        matches = re.findall(pattern, results_text)
        
        for subject, grade in matches:
            subject = subject.strip()
            grade = grade.upper()
            score = self.GRADE_SCORES.get(grade, 0.0)
            
            waec_results.append(WAECResult(
                subject=subject,
                grade=grade,
                score=score
            ))
        
        return waec_results
    
    def check_eligibility(self, waec_results: List[WAECResult], required_subjects: List[str]) -> bool:
        """
        Check if student meets basic eligibility requirements.
        All required subjects must be present with credit level (C6) or above.
        """
        student_subjects = {result.subject.lower(): result for result in waec_results}
        
        for required_subject in required_subjects:
            required_lower = required_subject.lower()
            
            # Check if subject is present
            found = False
            for student_subject, result in student_subjects.items():
                if required_lower in student_subject or student_subject in required_lower:
                    if result.score >= 0.5:  # C6 or above
                        found = True
                        break
            
            if not found:
                return False
        
        return True
    
    def calculate_waec_fit(self, waec_results: List[WAECResult], required_subjects: List[str]) -> float:
        """
        Calculate WAEC fit score.
        70% subject combo match + 30% grade quality
        """
        if not waec_results:
            return 0.0
        
        # Subject combo match (70%)
        subject_combo_score = 1.0 if self.check_eligibility(waec_results, required_subjects) else 0.0
        
        # Grade quality in required subjects (30%)
        student_subjects = {result.subject.lower(): result for result in waec_results}
        relevant_scores = []
        
        for required_subject in required_subjects:
            required_lower = required_subject.lower()
            for student_subject, result in student_subjects.items():
                if required_lower in student_subject or student_subject in required_lower:
                    relevant_scores.append(result.score)
                    break
        
        grade_quality_score = sum(relevant_scores) / len(relevant_scores) if relevant_scores else 0.0
        
        # Combine: 70% subject combo + 30% grade quality
        waec_fit = 0.70 * subject_combo_score + 0.30 * grade_quality_score
        return waec_fit
    
    def calculate_interest_fit(self, student_interests: str, program_description: str) -> float:
        """
        Calculate interest fit using cosine similarity.
        Returns value between 0 and 1.
        """
        if not self.embeddings:
            print("Embeddings not initialized. Returning default interest fit.")
            return 0.5  # Default neutral score
        
        try:
            # Get embeddings for both texts
            student_embedding = self.embeddings.embed_query(student_interests)
            program_embedding = self.embeddings.embed_query(program_description)
            
            # Calculate cosine similarity
            similarity = self._cosine_similarity(student_embedding, program_embedding)
            
            # Normalize to 0-1 range (cosine similarity can be -1 to 1)
            normalized_similarity = (similarity + 1) / 2
            return max(0.0, min(1.0, normalized_similarity))
            
        except Exception as e:
            print(f"Error calculating interest fit: {e}")
            return 0.5  # Default neutral score
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)
        
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def calculate_program_score(
        self,
        program_name: str,
        program_description: str,
        required_subjects: List[str],
        waec_results: List[WAECResult],
        student_interests: str
    ) -> ProgramScore:
        """
        Calculate final program score using the formula:
        Final Score = 0.65 × WAEC_fit + 0.35 × Interest_fit
        """
        # Check eligibility first
        eligibility_passed = self.check_eligibility(waec_results, required_subjects)
        
        if not eligibility_passed:
            return ProgramScore(
                program_name=program_name,
                eligibility_passed=False,
                waec_fit=0.0,
                interest_fit=0.0,
                final_score=0.0,
                explanation="Does not meet basic eligibility requirements"
            )
        
        # Calculate component scores
        waec_fit = self.calculate_waec_fit(waec_results, required_subjects)
        interest_fit = self.calculate_interest_fit(student_interests, program_description)
        
        # Calculate final score: 65% WAEC + 35% Interest
        final_score = 0.65 * waec_fit + 0.35 * interest_fit
        
        explanation = f"WAEC fit: {waec_fit:.2f}, Interest fit: {interest_fit:.2f}"
        
        return ProgramScore(
            program_name=program_name,
            eligibility_passed=True,
            waec_fit=waec_fit,
            interest_fit=interest_fit,
            final_score=final_score,
            explanation=explanation
        )
    
    def rank_programs(self, program_scores: List[ProgramScore]) -> List[ProgramScore]:
        """Rank programs by final score (highest first)."""
        eligible_programs = [p for p in program_scores if p.eligibility_passed]
        ineligible_programs = [p for p in program_scores if not p.eligibility_passed]
        
        # Sort eligible programs by score (descending)
        eligible_programs.sort(key=lambda x: x.final_score, reverse=True)
        
        # Return eligible programs first, then ineligible ones
        return eligible_programs + ineligible_programs
