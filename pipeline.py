import os
import re
import fitz # PyMuPDF
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_google_genai import ChatGoogleGenerativeAI
from dotenv import load_dotenv

load_dotenv()

# == Configuration ==
PDF_DIRECTORY = './documents' # <--- Set the directory path here
OUTPUT_FILENAME = 'oau_requirements_natural_language_llm.txt' # Output filename for LLM results

# Debugging flags
DEBUG_SAVE_CLEANED_TEXT = True # Save cleaned text for inspection
CLEANED_TEXT_DEBUG_FILENAME = 'cleaned_text_for_llm_input.txt'

# Set your Google API key (ensure GOOGLE_API_KEY environment variable is set)
# os.environ["GOOGLE_API_KEY"] = "YOUR_API_KEY" # Uncomment and replace if not using env var

# == 1. PDF Text Extraction (Same as before) ==
def extract_text_from_pdfs(directory_path):
    """Extracts text from all PDF files in a directory, sorted by filename."""
    all_text = ""
    enquiries_email = None

    if not os.path.isdir(directory_path):
        print(f"Error: Directory not found at {directory_path}")
        return "", None

    pdf_files = [f for f in os.listdir(directory_path) if f.lower().endswith('.pdf')]
    pdf_files.sort()

    print(f"Found {len(pdf_files)} PDF file(s) in {directory_path}")

    if not pdf_files:
        print("No PDF files found in the directory.")
        return "", None

    for i, pdf_file in enumerate(pdf_files):
        file_path = os.path.join(directory_path, pdf_file)
        page_text = ""
        try:
            doc = fitz.open(file_path)
            print(f"Processing {pdf_file} ({doc.page_count} pages)...")
            for page_num in range(doc.page_count):
                page = doc.load_page(page_num)
                page_text += page.get_text("text") + f"\n--- End of Page {page_num + 1} of {pdf_file} ---\n"

            doc.close()

            # Attempt to extract enquiries email from the first page processed
            if i == 0 and enquiries_email is None:
                enquiries_match = re.search(r"ENQUIRIES:\s*(\S+@\S+)", page_text, re.IGNORECASE)
                if enquiries_match:
                    enquiries_email = enquiries_match.group(1)

        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            continue

        all_text += page_text

    return all_text, enquiries_email

# == 2. Initial Cleaning & Preprocessing (Adjusted for LLM Input) ==
def clean_text_for_llm(text, enquiries_email):
    # Basic cleanup: remove definite noise like copyright/page numbers
    text = re.sub(r"©\s*\d{4}", "", text)
    text = re.sub(r"^\s*\d+\s*$", "", text, flags=re.MULTILINE)
    text = re.sub(r"--- End of Page \d+ of .*? ---", "\n---\n", text) # Replace page break with separator

    # Keep headers/footers for context, LLM can often filter them
    # text = re.sub(r"OR LEARNING AND CULTURE", "", text)
    # text = re.sub(r"OBAFEMI AWOLOWO UNIVERSITY,\s*ILE-IFE, NIGERIA", "", text)
    # text = re.sub(r"Requirements for Admission into Part I and Direct Entry in the Faculties", "", text)

    # Remove the specific enquiries line if found
    if enquiries_email:
         text = text.replace(f"ENQUIRIES: {enquiries_email}", "")
         text = text.replace("ENQUIRIES:", "") # Also remove the label

    # Standardize whitespace, but try to preserve relative spacing/structure
    # Fewer aggressive line removals than the regex parser version
    cleaned_lines = []
    for line in text.splitlines():
        stripped_line = line.strip()
        # Keep lines that had content, even if short, to help preserve table look
        if stripped_line:
            cleaned_lines.append(line) # Keep original spacing for LLM if line had content
        else:
            cleaned_lines.append("") # Preserve empty lines to some extent

    text = "\n".join(cleaned_lines).strip()
    # Collapse excessive blank lines to maximum two
    text = re.sub(r'\n\s*\n', '\n\n', text)


    return text

# == 3. LLM-based Extraction and Transformation ==
def extract_and_transform_with_llm(text_chunk):
    """Uses an LLM to extract and format requirements from a text chunk."""
    if not text_chunk.strip():
        return ""

    # Initialize the Google Gemini LLM
    # Use a suitable model, e.g., gemini-pro
    # Ensure GOOGLE_API_KEY is set in your environment
    try:
        llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash", temperature=0.3) # Lower temperature for factual extraction
    except Exception as e:
        print(f"Error initializing Google GenAI model: {e}")
        print("Please ensure the GOOGLE_API_KEY environment variable is set correctly.")
        return "Error: LLM initialization failed."


    # Define the prompt template
    # This prompt instructs the LLM on the task
    prompt_template = """
    You are an assistant that extracts and formats university admission requirements.
    Given the following text snippet from a university admission requirements document, identify and extract the requirements for each course mentioned.
    For each course found, list the requirements clearly under the following headings:
    - Unified Tertiary Matriculation Examination (UME) Requirements
    - Ordinary Level (O'Level) Requirements
    - Direct Entry Admission Requirements

    Present the requirements in a natural language, bulleted format. Expand abbreviations like B.Sc., ND, HND, NCE, A'Level, O/Level, UME. Clarify degree classifications like 2¹, 2² using standard terms (e.g., Second Class Lower Division, Second Class Upper Division).

    If a requirement type is not mentioned for a course in the text, state "Information not available in this snippet."
    If no courses are found in the text snippet, respond with "No relevant course requirements found in this snippet."

    Text Snippet:
    ```
    {text}
    ```

    Formatted Requirements:
    """

    prompt = PromptTemplate.from_template(prompt_template)

    # Create a simple chain: prompt -> llm -> output parser
    chain = prompt | llm | StrOutputParser()

    try:
        # Invoke the chain to process the text chunk
        formatted_output = chain.invoke({"text": text_chunk})
        return formatted_output.strip()
    except Exception as e:
        print(f"Error invoking LLM chain for chunk: {e}")
        # print(f"Chunk text: {text_chunk[:200]}...") # Optional: print part of the problematic chunk
        return f"Error processing requirements for a section: {e}"


# == Main Execution ==

# 1. Extract text from PDFs
full_text_content, enquiries_email = extract_text_from_pdfs(PDF_DIRECTORY)

if not full_text_content:
    print("No text extracted from PDFs. Exiting.")
else:
    # 2. Clean the extracted text (using the version suitable for LLM input)
    cleaned_text_content = clean_text_for_llm(full_text_content, enquiries_email)

    # Save cleaned text for debugging if flag is True
    if DEBUG_SAVE_CLEANED_TEXT:
        try:
            with open(CLEANED_TEXT_DEBUG_FILENAME, 'w', encoding='utf-8') as f:
                f.write(cleaned_text_content)
            print(f"Saved cleaned text to {CLEANED_TEXT_DEBUG_FILENAME} for inspection before LLM processing.")
        except IOError as e:
            print(f"Error saving cleaned text to {CLEANED_TEXT_DEBUG_FILENAME}: {e}")


    # == 3. Process text with LLM ==
    # This is a simplified example processing the whole cleaned text.
    # In a real RAG, you would split the cleaned_text_content into smaller
    # chunks first (using Langchain's TextSplitters) and process each chunk.
    # For demonstration, we'll just feed the whole cleaned text to the LLM.
    # BE AWARE OF LLM CONTEXT WINDOW LIMITS - this might fail on large documents.

    print("\n--- Sending text to LLM for extraction and transformation ---")

    # You might need to split the text into chunks here for larger documents
    # from langchain.text_splitter import RecursiveCharacterTextSplitter
    # text_splitter = RecursiveCharacterTextSplitter(chunk_size=2000, chunk_overlap=200) # Adjust chunk size based on LLM context window
    # chunks = text_splitter.split_text(cleaned_text_content)
    # print(f"Split text into {len(chunks)} chunks.")

    # For this example, process the whole cleaned text as one chunk (might be too large)
    # Or, take a smaller sample for testing
    text_to_process = cleaned_text_content # Process the whole cleaned text
    # text_to_process = cleaned_text_content[:5000] # Process only the first 5000 characters for testing

    # If processing chunks, you'd loop through chunks:
    # all_formatted_outputs = []
    # for i, chunk in enumerate(chunks):
    #     print(f"Processing chunk {i+1}/{len(chunks)}...")
    #     formatted_chunk_output = extract_and_transform_with_llm(chunk)
    #     all_formatted_outputs.append(formatted_chunk_output)
    # final_output = "\n\n".join(all_formatted_outputs) # Join results

    # For this simpler example, process the large text block directly (risks context window errors)
    # Or process the first few thousand characters for testing
    print(f"Sending {len(text_to_process)} characters to LLM...")
    final_output_text = extract_and_transform_with_llm(text_to_process)


    # == Output to File ==
    print(f"\n--- Writing output to {OUTPUT_FILENAME} ---")
    try:
        with open(OUTPUT_FILENAME, 'w', encoding='utf-8') as f:
            if final_output_text and "Error:" not in final_output_text:
                f.write(final_output_text)
                # The LLM is doing the parsing and structuring, so we don't count "entries" the same way
                print(f"Successfully wrote LLM-formatted output to {OUTPUT_FILENAME}.")
            else:
                f.write("LLM failed to process requirements or found none.\n")
                f.write(final_output_text) # Write the error message if any
                print("No entries written to file (LLM processing failed or found none).")


            f.write("\n" + "-" * 30 + "\n") # Separator before contact info
            if enquiries_email:
                f.write(f"For further enquiries, you can contact: {enquiries_email}\n")
                print(f"Enquiries email included: {enquiries_email}")
            else:
                 f.write("Enquiries email not found.\n")
                 print("Enquiries email not found.")

        print("Output file writing complete.")

    except IOError as e:
        print(f"Error writing to file {OUTPUT_FILENAME}: {e}")

    print("-" * 20)