from typing import List, Dict
from schema import ChatMessage

# In-memory storage for chat histories
# Key: session_id (str), Value: List of ChatMessage
chat_histories: Dict[str, List[ChatMessage]] = {}

class ChatService:
    """Manages chat history for different sessions."""
    def __init__(self, max_history_length: int = 10):
        self.max_history_length = max_history_length

    def get_history(self, session_id: str) -> List[ChatMessage]:
        """Retrieves the chat history for a given session."""
        return chat_histories.get(session_id, [])

    def add_message(self, session_id: str, message: ChatMessage):
        """Adds a message to the history and trims it if necessary."""
        if session_id not in chat_histories:
            chat_histories[session_id] = []

        chat_histories[session_id].append(message)

        # Trim history: keep only the last `max_history_length` messages
        if len(chat_histories[session_id]) > self.max_history_length:
            chat_histories[session_id] = chat_histories[session_id][-self.max_history_length:]

    def clear_history(self, session_id: str):
        """Clears the chat history for a specific session."""
        if session_id in chat_histories:
            del chat_histories[session_id]