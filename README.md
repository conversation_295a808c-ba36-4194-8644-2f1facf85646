# School Chatbot

A sophisticated chatbot application built with FastAPI that leverages RAG (Retrieval-Augmented Generation) to provide intelligent responses based on document knowledge. The application uses Google's Generative AI models and FAISS for efficient document retrieval.

## Features

- 🤖 Intelligent chat interface with context-aware responses
- 📚 Document-based knowledge retrieval using RAG
- 🔍 FAISS-powered semantic search
- 🔐 Secure session management
- 🎨 Modern web interface with static file serving
- 📝 Chat history management
- 🔄 Asynchronous request handling

## Prerequisites

- Python 3.8 or higher
- Google API Key for Generative AI
- Virtual environment (recommended)

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd schoolchatbot
```

2. Create and activate a virtual environment:

```bash
python -m venv venv
# On Windows
.\venv\Scripts\activate
# On Unix or MacOS
source venv/bin/activate
```

3. Install dependencies:

```bash
pip install -r requirements.txt
```

4. Create a `.env` file in the root directory with the following variables:

```env
GOOGLE_API_KEY=your_google_api_key
DOCUMENTS_DIR=documents
FAISS_INDEX_PATH=faiss_index
CHAT_HISTORY_LENGTH=10
EMBEDDING_MODEL=models/embedding-001
GENERATION_MODEL=gemini-1.5-flash-latest
SESSION_SECRET_KEY=your-secret-key
ENABLE_SCORING=true
```

## Project Structure

```
schoolchatbot/
├── documents/           # Directory for storing knowledge base documents
├── faiss_index/        # FAISS vector store for document retrieval
├── services/           # Core service implementations
├── static/            # Static files (CSS, JS, images)
├── templates/         # HTML templates
├── main.py           # FastAPI application entry point
├── routes.py         # API route definitions
├── schema.py         # Data models and schemas
└── requirements.txt  # Project dependencies
```

## Running the Application

1. Start the development server:

```bash
uvicorn main:app --reload
```

2. Access the application at `http://localhost:8000`

## API Endpoints

The application provides several API endpoints for chat interaction and document management. For detailed API documentation, visit:

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Development

### Adding New Documents

Place your documents in the `documents/` directory. The application will automatically process and index them during startup.

### Customizing Models

You can customize the embedding and generation models by modifying the environment variables:

- `EMBEDDING_MODEL`: For document embedding
- `GENERATION_MODEL`: For response generation

## Security Considerations

- Always use a strong `SESSION_SECRET_KEY` in production
- Keep your Google API key secure and never commit it to version control
- Regularly update dependencies to patch security vulnerabilities

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

[Add your license information here]

## Support

For support, please [add your contact information or support channels]
