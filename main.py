import os
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from starlette.middleware.sessions import SessionMiddleware
from dotenv import load_dotenv
import uvicorn

# Load environment variables from .env file
load_dotenv()

# Import services and schemas
from services.rag_service import RAGService
from services.chat_service import ChatService
from services.recommendation_service import RecommendationService
from services.enhanced_rag_service import EnhancedRAGService
from schema import ChatMessage # Needed for type hints in dependency getters

# --- Configuration ---
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
     print("WARNING: GOOGLE_API_KEY environment variable not set. RAG with Google GenAI may fail.")
     # Consider raising an error if the key is mandatory for startup
     # raise ValueError("GOOGLE_API_KEY environment variable not set")

DOCUMENTS_DIR = os.getenv("DOCUMENTS_DIR", "documents")
FAISS_INDEX_PATH = os.getenv("FAISS_INDEX_PATH", "faiss_index")
CHAT_HISTORY_LENGTH = int(os.getenv("CHAT_HISTORY_LENGTH", 10))
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "models/embedding-001")
GENERATION_MODEL = os.getenv("GENERATION_MODEL", "gemini-2.5-flash")
SESSION_SECRET_KEY = os.getenv("SESSION_SECRET_KEY", "a-default-secret-key-CHANGE-ME-IN-PRODUCTION")
ENABLE_SCORING = os.getenv("ENABLE_SCORING", "true").lower() == "true"
print(f"GENERATION_MODEL: {GENERATION_MODEL}")
print(f"SCORING ENABLED: {ENABLE_SCORING}")

# --- FastAPI App Setup ---
app = FastAPI()

# Add Session Middleware
app.add_middleware(SessionMiddleware, secret_key=SESSION_SECRET_KEY)

# Mount static files directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# --- Startup Event ---
@app.on_event("startup")
async def startup_event():
    """Initializes services and stores them on app.state."""
    print("App startup event triggered.")

    # Ensure necessary directories exist
    os.makedirs(DOCUMENTS_DIR, exist_ok=True)
    os.makedirs(FAISS_INDEX_PATH, exist_ok=True)
    os.makedirs("templates", exist_ok=True)
    os.makedirs("static", exist_ok=True)
    os.makedirs("services", exist_ok=True)


    # Initialize ChatService (lightweight)
    app.state.chat_service = ChatService(max_history_length=CHAT_HISTORY_LENGTH)
    print("Chat service initialized and stored on app.state.")

    # Initialize RAGService (can be time-consuming if index needs building)
    rag_service_instance = RAGService(
        documents_dir=DOCUMENTS_DIR,
        faiss_index_path=FAISS_INDEX_PATH,
        embedding_model=EMBEDDING_MODEL,
        generation_model=GENERATION_MODEL
    )

    # Call the blocking initialization method
    rag_service_instance.initialize_rag()

    # Store RAGService instance on app.state ONLY if it initialized properly
    # Check if the retrieval_chain was successfully created
    if rag_service_instance.retrieval_chain is not None:
        # Initialize recommendation service if scoring is enabled
        if ENABLE_SCORING:
            try:
                recommendation_service = RecommendationService(EMBEDDING_MODEL)
                if recommendation_service.initialize_embeddings():
                    enhanced_rag_service = EnhancedRAGService(rag_service_instance, recommendation_service)
                    app.state.rag_service = enhanced_rag_service
                    print("Enhanced RAG service with scoring initialized and stored on app.state.")
                else:
                    app.state.rag_service = rag_service_instance
                    print("Scoring initialization failed. Using regular RAG service.")
            except Exception as e:
                print(f"Error initializing scoring service: {e}. Using regular RAG service.")
                app.state.rag_service = rag_service_instance
        else:
            app.state.rag_service = rag_service_instance
            print("RAG service initialized and stored on app.state (scoring disabled).")
    else:
        app.state.rag_service = None # Explicitly store None if failed
        print("WARNING: RAG service did not initialize properly. Not stored on state. Chatbot may not function.")


    print("App startup finished.")

# --- Shutdown Event (Optional) ---
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup actions when the application stops."""
    print("App shutdown event triggered.")
    # Add cleanup logic if needed (e.g., closing connections)
    pass


# --- Dependency Getters (Retrieving from app.state) ---
# These functions are used by FastAPI's Depends() in routes.py
# They access the service instances stored on app.state

def get_rag_service(request: Request):
    """Dependency getter for RAGService (or EnhancedRAGService), retrieves from app.state."""
    rag_service = request.app.state.rag_service
    if rag_service is None:
         # Return a 503 if the RAG service failed to initialize
         raise HTTPException(status_code=503, detail="RAG service is not initialized or failed to start. Please check server logs.")
    return rag_service

def get_chat_service(request: Request) -> ChatService:
    """Dependency getter for ChatService, retrieves from app.state."""
    # ChatService is lightweight and should always be available if startup completes
    return request.app.state.chat_service

# --- Include Routers ---
# Import routes *after* defining the dependency getters in main.py
# and after setting up the app.state storage logic if needed.
import routes

# In this app.state approach, we DON'T patch the functions in routes.py.
# Instead, the routes.py file will directly import these getter functions
# from main.py.
# Remove these lines:
# routes.get_rag_service = get_rag_service # REMOVE
# routes.get_chat_service = get_chat_service # REMOVE

# Now include the router
app.include_router(routes.router)


# --- How to Run ---
# Save the above code as main.py
# Run from your terminal: uvicorn main:app --reload

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
