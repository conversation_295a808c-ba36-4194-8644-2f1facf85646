import uuid
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, Form, HTTPException, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.encoders import jsonable_encoder
# from starlette.middleware.sessions import SessionMiddleware # Not needed here

# Import services and schemas (keep these imports)
from services.rag_service import RAGService
from services.chat_service import ChatService
from schema import ChatRequest, ChatResponse, ChatMessage

# --- Import the dependency getters from main.py ---
# We are no longer patching; we are importing the functions directly
# Note: This creates a dependency of routes.py on main.py, which is acceptable
# for this app structure where main is the entry point.
from main import get_rag_service, get_chat_service # <-- Import the getters

# --- Templates Setup ---
templates = Jinja2Templates(directory="templates")

router = APIRouter()

# --- Routes ---

@router.get("/", response_class=HTMLResponse)
async def index(request: Request, chat_service: ChatService = Depends(get_chat_service)):
    """Serves the main chat HTML page and clears chat history."""
    # Get or create session ID
    session_id = request.session.get("session_id")
    if not session_id:
        session_id = str(uuid.uuid4())
        request.session["session_id"] = session_id
        print(f"New session created: {session_id}")
    else:
        # If a session exists, clear its history
        chat_service.clear_history(session_id)
        print(f"Chat history cleared for session: {session_id}")

    # The template response includes the request object, allowing access to session etc.
    return templates.TemplateResponse("chat.html", {"request": request})

@router.post("/chat", response_class=JSONResponse)
async def chat(
    request: Request, # Keep Request here as the dependency getters might need it
    chat_request: ChatRequest, # Automatically parses JSON request body
    # Inject services using the imported getters
    rag_service: RAGService = Depends(get_rag_service),
    chat_service: ChatService = Depends(get_chat_service)
):
    """Handles incoming chat messages, generates responses, and manages history."""
    # Get or create session ID
    session_id = request.session.get("session_id")
    if not session_id:
        session_id = str(uuid.uuid4())
        request.session["session_id"] = session_id
        print(f"New session created: {session_id}")

    user_message_content = chat_request.message

    # Get the chat history *before* adding the current user message
    prior_history = chat_service.get_history(session_id) # <--- This line should now work

    # Fetch personalization context from session
    results = request.session.get("results", "")
    hobbies = request.session.get("hobbies", "")
    likes = request.session.get("likes", "")
    extra_context = f"Secondary School Results: {results}\nHobbies: {hobbies}\nLikes: {likes}" if any([results, hobbies, likes]) else None

    # Generate response using RAG service, passing extra_context
    bot_response_content = rag_service.generate_response(user_message_content, prior_history, extra_context=extra_context)

    # Add both the user message and the bot response to the history
    user_message = ChatMessage(role="user", content=user_message_content)
    bot_message = ChatMessage(role="bot", content=bot_response_content)

    chat_service.add_message(session_id, user_message)
    chat_service.add_message(session_id, bot_message)

    # Return the bot response as JSON
    return JSONResponse(content=jsonable_encoder(ChatResponse(response=bot_response_content)))


# Remove the placeholder getter functions from here
# def get_rag_service() -> RAGService: ... # REMOVE
# def get_chat_service() -> ChatService: ... # REMOVE