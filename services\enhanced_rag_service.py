"""
Enhanced RAG service that optionally integrates recommendation scoring.
This extends the existing RAG service without breaking it.
"""

from typing import List, Optional, Dict, Any
from services.rag_service import RAGService
from services.recommendation_service import RecommendationService, ProgramScore
from schema import ChatMessage
import re


class EnhancedRAGService:
    """
    Enhanced RAG service that can optionally provide scored recommendations.
    Falls back to regular RAG if scoring fails.
    """
    
    def __init__(self, rag_service: RAGService, recommendation_service: Optional[RecommendationService] = None):
        """Initialize with existing RAG service and optional recommendation service."""
        self.rag_service = rag_service
        self.recommendation_service = recommendation_service
        self.scoring_enabled = recommendation_service is not None
    
    def generate_response(
        self, 
        query: str, 
        chat_history: List[ChatMessage], 
        extra_context: str = None,
        enable_scoring: bool = True
    ) -> str:
        """
        Generate response with optional scoring enhancement.
        Falls back to regular RAG if scoring fails or is disabled.
        """
        # Always get the regular RAG response first
        rag_response = self.rag_service.generate_response(query, chat_history, extra_context)
        
        # If scoring is disabled or not available, return regular response
        if not enable_scoring or not self.scoring_enabled:
            return rag_response
        
        # Try to enhance with scoring if conditions are met
        try:
            enhanced_response = self._try_enhance_with_scoring(query, extra_context, rag_response)
            return enhanced_response if enhanced_response else rag_response
        except Exception as e:
            print(f"Error in scoring enhancement: {e}")
            return rag_response  # Fallback to regular RAG
    
    def _try_enhance_with_scoring(self, query: str, extra_context: str, rag_response: str) -> Optional[str]:
        """
        Try to enhance the response with scoring if WAEC results are detected.
        Returns None if enhancement is not applicable.
        """
        if not extra_context:
            return None
        
        # Extract WAEC results from extra_context
        waec_results = self._extract_waec_from_context(extra_context)
        if not waec_results:
            return None
        
        # Extract interests from extra_context
        interests = self._extract_interests_from_context(extra_context)
        if not interests:
            return None
        
        # Try to extract programs mentioned in RAG response
        programs = self._extract_programs_from_response(rag_response)
        if not programs:
            return None
        
        # Calculate scores for mentioned programs
        program_scores = []
        for program_name, program_info in programs.items():
            try:
                # Extract required subjects (this is simplified - you might need more sophisticated extraction)
                required_subjects = self._extract_required_subjects(program_info)
                
                score = self.recommendation_service.calculate_program_score(
                    program_name=program_name,
                    program_description=program_info,
                    required_subjects=required_subjects,
                    waec_results=waec_results,
                    student_interests=interests
                )
                program_scores.append(score)
            except Exception as e:
                print(f"Error scoring program {program_name}: {e}")
                continue
        
        if not program_scores:
            return None
        
        # Rank programs
        ranked_programs = self.recommendation_service.rank_programs(program_scores)
        
        # Enhance the response with scoring information
        return self._create_enhanced_response(rag_response, ranked_programs)
    
    def _extract_waec_from_context(self, extra_context: str) -> List:
        """Extract WAEC results from extra context."""
        if "Secondary School Results:" not in extra_context:
            return []
        
        # Extract the results line
        lines = extra_context.split('\n')
        results_line = ""
        for line in lines:
            if "Secondary School Results:" in line:
                results_line = line.replace("Secondary School Results:", "").strip()
                break
        
        if not results_line:
            return []
        
        return self.recommendation_service.parse_waec_results(results_line)
    
    def _extract_interests_from_context(self, extra_context: str) -> str:
        """Extract interests from extra context."""
        interests_parts = []
        
        lines = extra_context.split('\n')
        for line in lines:
            if "Hobbies:" in line:
                hobbies = line.replace("Hobbies:", "").strip()
                if hobbies:
                    interests_parts.append(hobbies)
            elif "Likes:" in line:
                likes = line.replace("Likes:", "").strip()
                if likes:
                    interests_parts.append(likes)
        
        return " ".join(interests_parts)
    
    def _extract_programs_from_response(self, rag_response: str) -> Dict[str, str]:
        """
        Extract program names and descriptions from RAG response.
        This is a simplified implementation - you might need more sophisticated extraction.
        """
        programs = {}
        
        # Look for common program patterns
        # This is a basic implementation - you might want to make this more sophisticated
        program_patterns = [
            r'\*\*([A-Z][a-zA-Z\s]+)\*\*',  # **Program Name**
            r'([A-Z][a-zA-Z\s]+) \(B\.Sc\)',  # Program Name (B.Sc)
            r'([A-Z][a-zA-Z\s]+) program',  # Program Name program
        ]
        
        for pattern in program_patterns:
            matches = re.findall(pattern, rag_response)
            for match in matches:
                program_name = match.strip()
                if len(program_name) > 3:  # Filter out very short matches
                    # Use a portion of the response as program description
                    programs[program_name] = rag_response[:500]  # First 500 chars as description
        
        return programs
    
    def _extract_required_subjects(self, program_info: str) -> List[str]:
        """
        Extract required subjects from program information.
        This is a simplified implementation.
        """
        # Common required subjects for most programs
        default_subjects = ["English Language", "Mathematics"]
        
        # Look for subject mentions in the text
        subjects = set(default_subjects)
        
        subject_keywords = [
            "Physics", "Chemistry", "Biology", "Geography", "Economics",
            "Government", "Literature", "History", "Agricultural Science",
            "Further Mathematics", "Technical Drawing"
        ]
        
        program_info_lower = program_info.lower()
        for subject in subject_keywords:
            if subject.lower() in program_info_lower:
                subjects.add(subject)
        
        return list(subjects)
    
    def _create_enhanced_response(self, original_response: str, ranked_programs: List[ProgramScore]) -> str:
        """Create an enhanced response with scoring information."""
        if not ranked_programs:
            return original_response
        
        # Add scoring summary to the response
        enhancement = "\n\n**📊 Personalized Recommendations Based on Your Results:**\n\n"
        
        for i, program in enumerate(ranked_programs[:3], 1):  # Top 3 programs
            if program.eligibility_passed:
                match_strength = "Excellent" if program.final_score >= 0.8 else "Good" if program.final_score >= 0.6 else "Fair"
                enhancement += f"{i}. **{program.program_name}** - {match_strength} Match ({program.final_score:.1%})\n"
                enhancement += f"   *{program.explanation}*\n\n"
            else:
                enhancement += f"❌ **{program.program_name}** - {program.explanation}\n\n"
        
        return original_response + enhancement
    
    # Delegate other methods to the original RAG service
    def __getattr__(self, name):
        """Delegate unknown attributes to the original RAG service."""
        return getattr(self.rag_service, name)
