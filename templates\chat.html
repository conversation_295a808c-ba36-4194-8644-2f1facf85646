<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>School Chatbot</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  </head>
  <body class="bg-gray-100 min-h-screen flex flex-col">
    <div class="flex flex-col items-center justify-center min-h-screen">
      <div
        class="w-full max-w-2xl bg-white rounded-xl shadow-lg flex flex-col h-[80vh]"
      >
        <header
          class="px-6 py-4 border-b border-gray-200 flex items-center justify-between"
        >
          <h1 class="text-xl font-bold text-blue-700">OAU School Chatbot</h1>
        </header>
        <main
          id="chatbox"
          class="flex-1 overflow-y-auto px-6 py-4 space-y-4 bg-gray-50"
        >
          <!-- Chat messages will be injected here by JS -->
        </main>
        <form
          id="chat-form"
          class="flex items-center gap-2 px-6 py-4 border-t border-gray-200 bg-white"
        >
          <input
            id="user-input"
            type="text"
            placeholder="Type your message..."
            autocomplete="off"
            class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400"
            required
          />
          <button
            type="submit"
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-5 py-2 rounded-lg transition"
          >
            Send
          </button>
        </form>
      </div>
    </div>
    <script>
      // Simple chat logic (replace with your own JS as needed)
      const chatForm = document.getElementById("chat-form");
      const chatbox = document.getElementById("chatbox");

      // Initial welcome message (markdown supported)
      const welcomeText = `Hello there! Welcome! I'm your dedicated AI guide for everything about **Obafemi Awolowo University (OAU), Ile-Ife, Nigeria**.\n\nAsk me about course requirements, program structures, or academic regulations!`;
      addMessage(welcomeText, "bot");

      chatForm.addEventListener("submit", async (e) => {
        e.preventDefault();
        const input = document.getElementById("user-input");
        const userMsg = input.value.trim();
        if (!userMsg) return;
        addMessage(userMsg, "user");
        input.value = "";
        chatbox.scrollTop = chatbox.scrollHeight;
        // Send to backend
        const res = await fetch("/chat", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMsg }),
        });
        const data = await res.json();
        addMessage(data.response, "bot");
        chatbox.scrollTop = chatbox.scrollHeight;
      });
      function addMessage(text, role) {
        const msgDiv = document.createElement("div");
        msgDiv.className = `max-w-[80%] rounded-xl px-4 py-2 mb-2 ${
          role === "user"
            ? "bg-blue-600 text-white self-end ml-auto"
            : "bg-gray-200 text-gray-900 self-start mr-auto"
        }`;
        if (role === "bot") {
          msgDiv.innerHTML = marked.parse(text);
        } else {
          msgDiv.textContent = text;
        }
        chatbox.appendChild(msgDiv);
      }
    </script>
  </body>
</html>
