<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OAU School Chatbot</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        /* Add some basic animations for chat bubbles */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .chat-bubble {
            animation: fadeIn 0.3s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="flex flex-col h-screen">
        <!-- Header -->
        <header class="bg-white border-b border-gray-200 px-4 sm:px-6 py-3">
            <div class="max-w-5xl mx-auto flex items-center justify-between">
                <h1 class="text-xl sm:text-2xl font-bold text-gray-800">🎓 Obafemi Awolowo University Program Recommendation Assistant (OAU-PRA)
                </h1>
            </div>
        </header>

        <!-- Chatbox -->
        <main id="chatbox" class="flex-1 overflow-y-auto p-4 sm:p-6 space-y-4">
            <!-- Chat messages will be injected here -->
        </main>

        <!-- Input Form -->
        <footer class="bg-white border-t border-gray-200 px-4 sm:px-6 py-3">
            <div class="max-w-5xl mx-auto">
                <form id="chat-form" class="flex items-center gap-2 sm:gap-4">
                    <input id="user-input" type="text" placeholder="Type your message..." autocomplete="off"
                        class="flex-1 w-full px-4 py-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 transition-shadow"
                        required />
                    <button type="submit"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-5 py-3 rounded-full transition-transform transform hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M12 5l7 7-7 7" />
                        </svg>
                    </button>
                </form>
            </div>
        </footer>
    </div>

    <script>
        const chatForm = document.getElementById("chat-form");
        const chatbox = document.getElementById("chatbox");
        const userInput = document.getElementById("user-input");

        // Initial welcome message
        const welcomeText = `
**Welcome to your personalized academic journey!**

• I am OAU-PRA, your dedicated AI guide for Program Recommendations at Obafemi Awolowo University.

• **Ready to find your perfect program?** Share your results and interests to discover suitable programs and course requirements.

• **Example:** "My WAEC results are: English (B3), Mathematics (A1), Physics (A1), Chemistry (B2), Biology (B3), etc. I am passionate about engineering and innovation. What program(s) at Obafemi Awolowo University can I study?"

**Let's get started! 🚀**`;
        addMessage(welcomeText, "bot");

        chatForm.addEventListener("submit", async (e) => {
            e.preventDefault();
            const userMsg = userInput.value.trim();
            if (!userMsg) return;

            addMessage(userMsg, "user");
            userInput.value = "";
            scrollToBottom();

            // Add a thinking indicator
            const thinkingIndicator = addMessage("...", "bot", true);

            try {
                const res = await fetch("/chat", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ message: userMsg }),
                });
                const data = await res.json();
                
                // Remove thinking indicator and add the actual response
                thinkingIndicator.remove();
                addMessage(data.response, "bot");
                scrollToBottom();
            } catch (error) {
                console.error("Error fetching chat response:", error);
                thinkingIndicator.remove();
                addMessage("Sorry, something went wrong. Please try again.", "bot");
                scrollToBottom();
            }
        });

        function addMessage(text, role, isThinking = false) {
            const msgWrapper = document.createElement("div");
            msgWrapper.className = `flex ${role === 'user' ? 'justify-end' : 'justify-start'} chat-bubble`;

            const msgDiv = document.createElement("div");
            msgDiv.className = `max-w-md lg:max-w-2xl rounded-2xl px-5 py-3 shadow ${
                role === "user"
                    ? "bg-blue-600 text-white"
                    : "bg-white text-gray-800"
            }`;

            if (isThinking) {
                msgDiv.innerHTML = `<div class="flex items-center justify-center space-x-2">
                                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                                    </div>`;
            } else if (role === "bot") {
                msgDiv.innerHTML = marked.parse(text);
            } else {
                msgDiv.textContent = text;
            }
            
            msgWrapper.appendChild(msgDiv);
            chatbox.appendChild(msgWrapper);
            return msgWrapper;
        }

        function scrollToBottom() {
            chatbox.scrollTop = chatbox.scrollHeight;
        }
    </script>
</body>
</html>
