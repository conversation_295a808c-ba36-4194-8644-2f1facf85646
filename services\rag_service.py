import os
from typing import List
from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import PyPDFLoader, TextLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.messages import HumanMessage, AIMessage
import asyncio # Might need this if we ever switch to async initialization

from schema import ChatMessage

class RAGService:
    """Handles the RAG pipeline: indexing and generation."""
    def __init__(self, documents_dir: str, faiss_index_path: str, embedding_model: str, generation_model: str):
        self.documents_dir = documents_dir
        self.faiss_index_path = faiss_index_path
        self.embedding_model = embedding_model
        self.generation_model = generation_model

        self.vectorstore = None
        self.retrieval_chain = None
        self.llm = None
        self.embeddings = None

    # Corrected initialize_rag with load/build logic
    def initialize_rag(self):
        """Initializes embeddings, LLM, and loads/builds the FAISS index."""
        print("Initializing RAG components...")
        try:
            self.embeddings = GoogleGenerativeAIEmbeddings(model=self.embedding_model)
            self.llm = ChatGoogleGenerativeAI(model=self.generation_model, temperature=0.3)

            # --- Index Loading/Building Logic ---
            index_file_path = os.path.join(self.faiss_index_path, 'index.faiss')
            pkl_file_path = os.path.join(self.faiss_index_path, 'index.pkl')

            index_fully_exists = os.path.exists(index_file_path) and os.path.exists(pkl_file_path)

            if index_fully_exists:
                print(f"Attempting to load FAISS index from {self.faiss_index_path}...")
                try:
                    # Important: allow_dangerous_deserialization=True needed for local files
                    self.vectorstore = FAISS.load_local(
                        self.faiss_index_path,
                        self.embeddings,
                        allow_dangerous_deserialization=True
                    )
                    print("Index loaded successfully.")
                except Exception as e:
                    print(f"Error loading index: {e}. Attempting to rebuild index.")
                    self.vectorstore = None # Ensure it's None before trying to build
            else:
                print(f"FAISS index not found at {self.faiss_index_path} or incomplete. Building new index.")
                self.vectorstore = None # Ensure it's None before trying to build

            # If vectorstore is still None (either no index existed or loading failed), build it
            if self.vectorstore is None:
                self._build_and_save_index() # This method sets self.vectorstore if successful

            # --- End Index Loading/Building Logic ---

            # Now, check if the vectorstore was successfully created or loaded
            if self.vectorstore:
                 print("Vector store is available. Creating retrieval chain.")
                 conversational_rag_prompt = conversational_rag_prompt = ChatPromptTemplate.from_messages([
    ("system", """
    You are an AI assistant for Obafemi Awolowo University (OAU), Ile-Ife, Nigeria. Your purpose is to provide information about the university's academic programs and requirements.
    You have access to detailed OAU academic data. Use this data to answer questions about course requirements, program structures, prerequisites, and regulations.
    For broader questions, use your general knowledge about OAU.

    Here is the context to use:
    {context}

    Instructions for responding:
    1.  Be direct and to the point. Do not use conversational fillers.
    2.  Responses should be concise, but include the recommended courses, O-level requirements, and a brief reason for the recommendation.
    3.  Prioritize using the specific OAU academic information from the context.
    4.  If the user expresses academic interests, recommend relevant OAU programs from the context.
    5.  Present information clearly, using bullet points or lists.
    6.  If a question is not about OAU, or asks for personal advice, politely decline.
    7.  Do not invent information. If you cannot answer, state that you don't have the information.
    8.  Never refer to "the context" or "the documents". Present information as your own knowledge.
    """),
    ("user", "{input}"),
])

                 document_chain = create_stuff_documents_chain(self.llm, conversational_rag_prompt)
                 self.retrieval_chain = create_retrieval_chain(self.vectorstore.as_retriever(), document_chain)
                 print("Retrieval chain created.")
            else:
                 print("Vector store is still not available after initialization attempt. RAG generation will not work.")
                 self.retrieval_chain = None

        except Exception as e:
            print(f"An unexpected error occurred during RAG service initialization: {e}")
            self.vectorstore = None
            self.retrieval_chain = None


    def _build_and_save_index(self):
        """Loads documents, splits them, creates and saves the FAISS index."""
        if not os.path.exists(self.documents_dir):
             print(f"Documents directory not found: {self.documents_dir}")
             self.vectorstore = None
             return # Exit early if directory doesn't exist

        documents = []
        print(f"Loading documents from {self.documents_dir}...")
        # Make sure embeddings are initialized before trying to load/process documents
        if not self.embeddings:
             print("Embeddings not initialized. Cannot build index.")
             self.vectorstore = None
             return

        for filename in os.listdir(self.documents_dir):
            filepath = os.path.join(self.documents_dir, filename)
            if os.path.isfile(filepath):
                try:
                    # Add more loaders here as needed
                    if filename.endswith(".pdf"):
                         loader = PyPDFLoader(filepath)
                         documents.extend(loader.load())
                         print(f"Loaded {filepath}")
                         
                    elif filename.endswith(".txt"):
                         loader = TextLoader(filepath)
                         documents.extend(loader.load())
                         print(f"Loaded {filepath}")
                    else:
                         print(f"Skipping unsupported file type: {filename}")
                except Exception as e:
                    print(f"Error loading {filepath}: {e}")

        if not documents:
            print(f"No supported documents found in {self.documents_dir}. RAG will not function.")
            self.vectorstore = None
            return # Exit early if no documents loaded

        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        splits = text_splitter.split_documents(documents)
        print(f"Split documents into {len(splits)} chunks.")

        print("Creating FAISS index...")
        try:
            self.vectorstore = FAISS.from_documents(splits, self.embeddings)
            print("Index created. Saving...")
            os.makedirs(self.faiss_index_path, exist_ok=True)
            self.vectorstore.save_local(self.faiss_index_path)
            print(f"Index saved to {self.faiss_index_path}")
        except Exception as e:
            print(f"Error building or saving index: {e}")
            self.vectorstore = None


    def generate_response(self, query: str, chat_history: List[ChatMessage], extra_context: str = None) -> str:
        """Generates a response using the RAG retrieval chain. Optionally prepends extra_context to the query."""
        if not self.retrieval_chain:
            # This message is printed if the RAG system didn't initialize correctly
            return "RAG system is not fully initialized or has no documents. Please check server logs."

        try:
            formatted_history = []
            for msg in chat_history:
                 if msg.role == 'user':
                      formatted_history.append(HumanMessage(content=msg.content))
                 elif msg.role == 'bot':
                      formatted_history.append(AIMessage(content=msg.content))

            # Prepend extra_context to the query if provided
            if extra_context:
                full_query = f"{extra_context}\n\n{query}"
            else:
                full_query = query

            response = self.retrieval_chain.invoke({"input": full_query, "chat_history": formatted_history})
            return response.get("answer", "Could not generate a valid response from the documents.")

        except Exception as e:
            print(f"Error during RAG generation: {e}")
            return "An internal error occurred while generating the response."