body {
  background: #f4f6fb;
  font-family: "Segoe UI", Arial, sans-serif;
  margin: 0;
  padding: 0;
}
.container {
  max-width: 420px;
  margin: 60px auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
  padding: 32px 28px 24px 28px;
}
h2 {
  text-align: center;
  color: #2d3a4a;
  margin-bottom: 28px;
}
label {
  display: block;
  margin-bottom: 6px;
  color: #3a4a5d;
  font-weight: 500;
}
input[type="text"],
textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d9e6;
  border-radius: 6px;
  margin-bottom: 18px;
  font-size: 1rem;
  background: #f9fafc;
  transition: border 0.2s;
}
input[type="text"]:focus,
textarea:focus {
  border: 1.5px solid #4f8cff;
  outline: none;
  background: #fff;
}
button[type="submit"] {
  width: 100%;
  background: #4f8cff;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 12px 0;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 8px;
}
button[type="submit"]:hover {
  background: #2563eb;
}
@media (max-width: 600px) {
  .container {
    padding: 18px 6vw 16px 6vw;
    margin: 24px 0;
  }
  h2 {
    font-size: 1.3rem;
  }
}
